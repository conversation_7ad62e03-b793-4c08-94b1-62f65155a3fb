"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { navigationData } from "@/lib/navigation-data"

export function SubNavigation() {
  const pathname = usePathname()
  const router = useRouter()

  // Find the current active main item and its sub-modules
  const { activeMainItem, subModules } = React.useMemo(() => {
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        if (mainItem.subModules && mainItem.subModules.length > 0) {
          return { activeMainItem: mainItem, subModules: mainItem.subModules }
        }
      }
    }
    return { activeMainItem: null, subModules: null }
  }, [pathname])

  // No auto-redirect needed since navigation URLs now point directly to first sub-module

  // Don't render if no active main item or no sub-modules
  if (!activeMainItem || !subModules || subModules.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center gap-2">
      {subModules.map((subModule) => {
        // Enhanced active state detection for nested routes
        // For asset detail pages like /assets/it/it-001, we want /assets/it to be active
        const isActive = pathname === subModule.url ||
          (subModule.url !== "/assets" && pathname.startsWith(subModule.url + "/"))

        return (
          <Button
            key={subModule.url}
            variant="ghost"
            size="sm"
            asChild
            className={cn(
              "h-8 px-4 text-sm font-medium transition-all duration-200",
              "hover:bg-accent hover:text-accent-foreground",
              "text-muted-foreground",
              isActive && [
                "border border-border bg-accent text-accent-foreground font-medium shadow-sm"
              ]
            )}
          >
            <Link href={subModule.url}>
              {subModule.title}
            </Link>
          </Button>
        )
      })}
    </nav>
  )
}
