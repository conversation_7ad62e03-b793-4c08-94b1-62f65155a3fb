"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function ActivityPage() {
  const router = useRouter()

  useEffect(() => {
    router.replace("/activity/tasks")
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <h2 className="text-lg font-medium">Redirecting to Tasks...</h2>
        <p className="text-sm text-muted-foreground">Please wait while we redirect you to the Activity Tasks page.</p>
      </div>
    </div>
  )
}