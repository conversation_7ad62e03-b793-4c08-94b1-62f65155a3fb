"use client"

import React from "react"
import { <PERSON>, CheckS<PERSON>re, <PERSON>, <PERSON>, TrendingUp, MessageSquare } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"

import { PersonalTaskDashboard } from "@/components/activity/personal-task-dashboard"
import { NotificationCenter } from "@/components/activity/notification-center"
import { ApprovalWorkflowInterface } from "@/components/activity/approval-workflow-interface"
import { CommunicationHub } from "@/components/activity/communication-hub"
import { KnowledgeContextPanel } from "@/components/activity/knowledge-context-panel"

import { 
  sampleActivityDashboard, 
  sampleTasks, 
  sampleNotifications, 
  sampleApprovals,
  sampleActivityMetrics,
  sampleCommunicationThreads,
  sampleKnowledgeEntries
} from "@/lib/activity-data"

export default function ActivityPage() {
  const dashboard = sampleActivityDashboard

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Activity Center</h1>
            <p className="text-muted-foreground">
              Personal command center for tasks, notifications, approvals, and collaboration
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Bell className="mr-2 h-4 w-4" />
              Preferences
            </Button>
            <Button variant="outline" size="sm">
              <TrendingUp className="mr-2 h-4 w-4" />
              Analytics
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <CheckSquare className="h-4 w-4" />
              Pending Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboard.pendingTasks}</div>
            <p className="text-xs text-muted-foreground">
              {dashboard.overdueItems} overdue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4" />
              Upcoming Deadlines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboard.upcomingDeadlinesCount}</div>
            <p className="text-xs text-muted-foreground">
              Next 7 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Bell className="h-4 w-4" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboard.unreadNotifications}</div>
            <p className="text-xs text-muted-foreground">
              Unread alerts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4" />
              Approvals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboard.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <TrendingUp className="h-4 w-4" />
              Productivity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboard.monthlyTrends.productivity}%</div>
            <Progress value={dashboard.monthlyTrends.productivity} className="h-2 mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Grid */}
      <div className="flex-1 grid gap-6 lg:grid-cols-3">
        {/* Left Column - Tasks and Approvals */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Task Management Dashboard */}
          <PersonalTaskDashboard 
            tasks={sampleTasks}
            metrics={sampleActivityMetrics}
            upcomingDeadlines={dashboard.upcomingDeadlines}
          />

          {/* Approval & Review Workflows Interface */}
          <ApprovalWorkflowInterface 
            approvals={sampleApprovals}
            pendingCount={dashboard.pendingApprovals}
          />

          {/* Communication Hub */}
          <CommunicationHub 
            threads={sampleCommunicationThreads}
            recentActivity={dashboard.recentCommunications}
          />
        </div>

        {/* Right Column - Notifications and Context */}
        <div className="space-y-6">
          {/* Role-Based Notification Center */}
          <NotificationCenter 
            notifications={sampleNotifications}
            unreadCount={dashboard.unreadNotifications}
          />

          {/* Performance Analytics Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Performance Analytics
              </CardTitle>
              <CardDescription>
                Personal productivity metrics and performance insights
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Completion Rate</span>
                    <span className="text-sm font-bold">{sampleActivityMetrics.completionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={sampleActivityMetrics.completionRate} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Quality Score</span>
                    <span className="text-sm font-bold">{sampleActivityMetrics.qualityScore.toFixed(1)}/10</span>
                  </div>
                  <Progress value={sampleActivityMetrics.qualityScore * 10} className="h-2" />
                </div>
              </div>
              <div className="pt-4 border-t">
                <Button variant="outline" size="sm" className="w-full">
                  View Detailed Analytics
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Knowledge Base and Context */}
          <KnowledgeContextPanel 
            knowledgeEntries={sampleKnowledgeEntries}
            relatedTasks={dashboard.recentTasks}
          />
        </div>
      </div>

      {/* Recommendations Section */}
      {dashboard.recommendations.length > 0 && (
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Recommendations
              </CardTitle>
              <CardDescription>
                AI-powered suggestions to improve your productivity and collaboration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {dashboard.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{recommendation.title}</h4>
                        <Badge variant="outline">
                          {recommendation.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {recommendation.description}
                      </p>
                    </div>
                    {recommendation.actionUrl && (
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
